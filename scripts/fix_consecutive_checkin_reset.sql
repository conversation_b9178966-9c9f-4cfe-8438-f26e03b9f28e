-- Fix consecutive checkin tasks to have daily reset period
-- This allows them to be reset daily so users can repeat the tasks

-- Update CONSECUTIVE_CHECKIN_3 task
UPDATE activity_tasks 
SET reset_period = 'DAILY'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_3' 
AND reset_period IS NULL;

-- Update CONSECUTIVE_CHECKIN_7 task  
UPDATE activity_tasks 
SET reset_period = 'DAILY'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_7' 
AND reset_period IS NULL;

-- Update CONSECUTIVE_CHECKIN_30 task
UPDATE activity_tasks 
SET reset_period = 'DAILY'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_30' 
AND reset_period IS NULL;

-- Reset all completed consecutive checkin tasks to allow them to be done again
-- This will reset the progress but preserve the streak count
UPDATE user_task_progress 
SET 
    status = 'NOT_STARTED',
    progress_value = 0,
    last_reset_at = NOW(),
    updated_at = NOW()
WHERE task_id IN (
    SELECT id FROM activity_tasks 
    WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
) 
AND status IN ('COMPLETED', 'CLAIMED');

-- Log the changes
SELECT 
    'Updated tasks:' as message,
    COUNT(*) as count
FROM activity_tasks 
WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
AND reset_period = 'DAILY';

SELECT 
    'Reset user progress:' as message,
    COUNT(*) as count
FROM user_task_progress 
WHERE task_id IN (
    SELECT id FROM activity_tasks 
    WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
) 
AND status = 'NOT_STARTED';
