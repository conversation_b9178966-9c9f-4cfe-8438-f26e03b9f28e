package level

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
)

// LevelUpgradeIntegrationTestSuite tests the integration between Level Upgrade Task and Activity Cashback
type LevelUpgradeIntegrationTestSuite struct {
	suite.Suite
	task *LevelUpgradeTask
	ctx  context.Context
}

func (suite *LevelUpgradeIntegrationTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.task = NewLevelUpgradeTask()
	
	// Initialize logger to prevent nil pointer dereference
	if global.GVA_LOG == nil {
		global.GVA_LOG = zap.NewNop() // No-op logger for tests
	}
}

func (suite *LevelUpgradeIntegrationTestSuite) TestTriggerAccumulatedTradingTasksCheck() {
	// Test that the trigger function can be called without errors
	testUserID := uuid.New()
	
	// This should not panic or error
	suite.NotPanics(func() {
		suite.task.triggerAccumulatedTradingTasksCheck(testUserID)
	}, "triggerAccumulatedTradingTasksCheck should not panic")
}

func (suite *LevelUpgradeIntegrationTestSuite) TestLevelUpgradeTaskCreation() {
	// Test that LevelUpgradeTask can be created
	task := NewLevelUpgradeTask()
	suite.NotNil(task, "LevelUpgradeTask should be created successfully")
}

func (suite *LevelUpgradeIntegrationTestSuite) TestIntegrationWorkflow() {
	// Test the conceptual workflow (without database)
	suite.T().Run("Workflow concept test", func(t *testing.T) {
		testUserID := uuid.New()
		
		// Step 1: Level Upgrade Task would calculate volume
		totalVolume := decimal.NewFromFloat(75000) // Should complete 10K and 50K tasks
		
		// Step 2: Level Upgrade Task would update user_tier_info.trading_volume_usd
		// (This would normally happen in updateUserTierInfoTradingVolume)
		
		// Step 3: Level Upgrade Task triggers accumulated trading tasks check
		// This should call the activity cashback service to check milestones
		suite.NotPanics(func() {
			suite.task.triggerAccumulatedTradingTasksCheck(testUserID)
		}, "Integration trigger should work without panic")
		
		t.Logf("Integration workflow test completed for user %s with volume %.0f", 
			testUserID.String(), totalVolume.InexactFloat64())
	})
}

func TestLevelUpgradeIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(LevelUpgradeIntegrationTestSuite))
}
