package activity_cashback

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConsecutiveCheckinResetLogic tests the reset logic for consecutive checkin tasks
func TestConsecutiveCheckinResetLogic(t *testing.T) {
	t.Run("Progressive_Task_Reset_Logic", func(t *testing.T) {
		// Test that progressive tasks use ResetProgressive method
		progress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          uuid.New(),
			TaskID:          uuid.New(),
			Status:          model.TaskStatusClaimed,
			ProgressValue:   3,
			StreakCount:     3,
			CompletionCount: 1,
		}

		// Simulate progressive reset
		progress.ResetProgressive()

		// Verify reset behavior
		assert.Equal(t, model.TaskStatusNotStarted, progress.Status)
		assert.Equal(t, 0, progress.ProgressValue)
		assert.Equal(t, 3, progress.StreakCount) // Streak should be preserved
		assert.NotNil(t, progress.LastResetAt)
	})

	t.Run("Regular_Task_Reset_Logic", func(t *testing.T) {
		// Test that regular tasks use Reset method
		progress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          uuid.New(),
			TaskID:          uuid.New(),
			Status:          model.TaskStatusClaimed,
			ProgressValue:   1,
			StreakCount:     5,
			CompletionCount: 1,
		}

		// Simulate regular reset
		progress.Reset()

		// Verify reset behavior
		assert.Equal(t, model.TaskStatusNotStarted, progress.Status)
		assert.Equal(t, 0, progress.ProgressValue)
		assert.Equal(t, 5, progress.StreakCount)     // Streak preserved (handled by task processor)
		assert.Equal(t, 0, progress.CompletionCount) // Completion count reset for regular tasks
		assert.NotNil(t, progress.LastResetAt)
	})

	t.Run("Consecutive_Checkin_Handler_Logic", func(t *testing.T) {
		// Test the logic for handling consecutive checkin
		// This is a unit test for the logic without database

		// Simulate user checking in on consecutive days
		today := time.Now().Truncate(24 * time.Hour)
		yesterday := today.Add(-24 * time.Hour)
		twoDaysAgo := today.Add(-48 * time.Hour)

		// Test case 1: First checkin
		lastCheckin := time.Time{} // Zero time = no previous checkin
		shouldIncrementStreak := lastCheckin.IsZero()
		assert.True(t, shouldIncrementStreak, "Should start streak on first checkin")

		// Test case 2: Consecutive checkin (yesterday -> today)
		lastCheckin = yesterday
		shouldIncrementStreak = lastCheckin.Equal(yesterday)
		assert.True(t, shouldIncrementStreak, "Should increment streak for consecutive days")

		// Test case 3: Gap in checkin (2 days ago -> today)
		lastCheckin = twoDaysAgo
		shouldResetStreak := !lastCheckin.Equal(yesterday) && !lastCheckin.IsZero()
		assert.True(t, shouldResetStreak, "Should reset streak when there's a gap")

		// Test case 4: Already checked in today
		lastCheckin = today
		shouldSkip := lastCheckin.Equal(today)
		assert.True(t, shouldSkip, "Should skip if already checked in today")
	})

	t.Run("Task_Seeder_Configuration", func(t *testing.T) {
		// Verify that consecutive checkin tasks have the correct configuration
		// This tests the seeder configuration

		expectedTasks := []struct {
			identifier  model.TaskIdentifier
			frequency   model.TaskFrequency
			resetPeriod model.ResetPeriod
			targetDays  int
		}{
			{model.TaskIDConsecutiveCheckin3, model.FrequencyProgressive, model.ResetDaily, 3},
			{model.TaskIDConsecutiveCheckin7, model.FrequencyProgressive, model.ResetDaily, 7},
			{model.TaskIDConsecutiveCheckin30, model.FrequencyProgressive, model.ResetDaily, 30},
		}

		for _, expected := range expectedTasks {
			t.Run(string(expected.identifier), func(t *testing.T) {
				// These would be the expected values from the seeder
				assert.Equal(t, model.FrequencyProgressive, expected.frequency)
				assert.Equal(t, model.ResetDaily, expected.resetPeriod)
			})
		}
	})
}

// TestConsecutiveCheckinScenario tests a complete scenario
func TestConsecutiveCheckinScenario(t *testing.T) {
	t.Run("Complete_3_Day_Consecutive_Scenario", func(t *testing.T) {
		// Simulate a complete 3-day consecutive checkin scenario
		userID := uuid.New()
		taskID := uuid.New()

		// Day 1: First checkin
		progress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusNotStarted,
			ProgressValue:   0,
			StreakCount:     0,
			CompletionCount: 0,
		}

		// Simulate first checkin
		progress.StreakCount = 1
		progress.ProgressValue = 1
		progress.Status = model.TaskStatusInProgress
		now := time.Now()
		progress.LastCompletedAt = &now

		assert.Equal(t, 1, progress.StreakCount)
		assert.Equal(t, 1, progress.ProgressValue)

		// Day 2: Second consecutive checkin
		progress.StreakCount = 2
		progress.ProgressValue = 2
		now = time.Now()
		progress.LastCompletedAt = &now

		assert.Equal(t, 2, progress.StreakCount)
		assert.Equal(t, 2, progress.ProgressValue)

		// Day 3: Third consecutive checkin - milestone reached
		progress.StreakCount = 3
		progress.ProgressValue = 3
		progress.Status = model.TaskStatusClaimed
		now = time.Now()
		progress.LastCompletedAt = &now

		assert.Equal(t, 3, progress.StreakCount)
		assert.Equal(t, 3, progress.ProgressValue)
		assert.Equal(t, model.TaskStatusClaimed, progress.Status)

		// Task gets reset daily (simulated)
		progress.ResetProgressive()

		// After reset, user can start again but streak is preserved
		assert.Equal(t, model.TaskStatusNotStarted, progress.Status)
		assert.Equal(t, 0, progress.ProgressValue)
		assert.Equal(t, 3, progress.StreakCount) // Streak preserved for consecutive tasks
	})
}
