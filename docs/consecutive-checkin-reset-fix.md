# Consecutive Check-in Task Reset Fix

## Problem Description

The consecutive check-in tasks (3-day, 7-day, 30-day) were not resetting properly after completion, preventing users from repeating these tasks. 

### Root Cause Analysis

1. **Missing Reset Period**: Consecutive check-in tasks were created with `frequency: "PROGRESSIVE"` but without a `reset_period`, meaning they never got reset by the daily task reset job.

2. **Permanent Completion**: Once a user completed a 3-day consecutive check-in task, it remained in `CLAIMED` status permanently, preventing the user from starting a new 3-day streak.

3. **No Daily Reset Logic**: The daily task reset job (`ResetDailyTasks`) only processed tasks with a defined `reset_period`, skipping progressive tasks.

### User Impact

- Users could only complete consecutive check-in tasks once
- After completing a 3-day streak, users couldn't start a new 3-day streak
- Tasks remained in `CLAIMED` status indefinitely

## Solution Implementation

### 1. Task Seeder Updates

**File**: `internal/service/activity_cashback/task_seeder.go`

Added `resetPeriod: model.ResetDaily` to all consecutive check-in tasks:

```go
{
    name:               "Log in continuously for 3 days",
    description:        "Consecutive sign-ins for 3 days to earn 50 points",
    taskIdentifier:     model.TaskIDConsecutiveCheckin3,
    points:             50,
    frequency:          model.FrequencyProgressive,
    resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0], // NEW
    // ... other fields
},
```

### 2. Progressive Task Reset Logic

**File**: `internal/model/user_task_progress.go`

Added new `ResetProgressive()` method that preserves streak count:

```go
// ResetProgressive resets progressive task progress but preserves streak for consecutive tasks
func (utp *UserTaskProgress) ResetProgressive() {
    now := time.Now()
    utp.Status = TaskStatusNotStarted
    utp.ProgressValue = 0
    // Don't reset CompletionCount for progressive tasks - they can be completed multiple times
    utp.LastResetAt = &now
    utp.UpdatedAt = now
    
    // For consecutive checkin tasks, preserve streak count
    // The streak will be managed by the task handler logic
}
```

### 3. Task Management Service Updates

**File**: `internal/service/activity_cashback/task_management_service.go`

Updated `ResetDailyTasks()` to handle progressive tasks differently:

```go
for _, progress := range tasks {
    // Check if this is a progressive task (like consecutive checkin)
    if progress.Task.Frequency == model.FrequencyProgressive {
        // For progressive tasks, use special reset logic that preserves streak
        progress.ResetProgressive()
    } else {
        // For regular daily tasks, use normal reset
        progress.Reset()
    }
    // ... update logic
}
```

### 4. Task Handler Improvements

**File**: `internal/service/activity_cashback/task_handlers.go`

Enhanced consecutive check-in handler with better logic:

- Prevent duplicate check-ins on the same day
- Better streak management
- Reset streak after milestone completion to start fresh

### 5. Database Migration

**File**: `scripts/fix_consecutive_checkin_reset.sql`

SQL script to fix existing data:

```sql
-- Update tasks to have daily reset period
UPDATE activity_tasks 
SET reset_period = 'DAILY'
WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30') 
AND reset_period IS NULL;

-- Reset completed tasks to allow repetition
UPDATE user_task_progress 
SET 
    status = 'NOT_STARTED',
    progress_value = 0,
    last_reset_at = NOW(),
    updated_at = NOW()
WHERE task_id IN (
    SELECT id FROM activity_tasks 
    WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
) 
AND status IN ('COMPLETED', 'CLAIMED');
```

## How It Works Now

### Daily Reset Process (UTC 00:00)

1. **Task Identification**: System identifies all tasks with `reset_period = 'DAILY'`
2. **Progressive Task Reset**: For consecutive check-in tasks:
   - Status → `NOT_STARTED`
   - Progress Value → `0`
   - Streak Count → **Preserved**
   - Last Reset Time → Updated
3. **Regular Task Reset**: For daily tasks like login:
   - Complete reset including completion count

### User Experience Flow

1. **Day 1-3**: User builds up 3-day consecutive streak
2. **Day 3**: Task completes, user gets 50 points, streak resets to 0
3. **Day 4 (UTC 00:00)**: Task automatically resets to `NOT_STARTED`
4. **Day 4+**: User can start a new 3-day consecutive streak

### Key Benefits

- ✅ Users can repeat consecutive check-in tasks indefinitely
- ✅ Streak logic works correctly for building consecutive days
- ✅ Tasks reset daily at UTC 00:00 like other daily tasks
- ✅ Backward compatible with existing user progress
- ✅ Maintains proper separation between task completion and streak tracking

## Testing

Comprehensive tests added in `consecutive_checkin_reset_test.go`:

- Progressive vs regular task reset behavior
- Consecutive check-in handler logic
- Complete 3-day scenario simulation
- Task configuration validation

## Deployment Steps

1. **Deploy Code**: Deploy the updated code with new reset logic
2. **Run Migration**: Execute `scripts/fix_consecutive_checkin_reset.sql`
3. **Verify**: Check that existing completed tasks are reset to `NOT_STARTED`
4. **Monitor**: Watch daily reset job logs to ensure progressive tasks are being reset

## Monitoring

Watch for these log messages:
- `"Daily tasks reset completed"` - Should include progressive tasks in count
- `"Consecutive check-in milestone reached"` - Users completing streaks
- `"Failed to reset daily task progress"` - Any reset failures

The fix ensures consecutive check-in tasks behave like repeatable daily challenges while maintaining proper streak tracking for the consecutive login mechanic.
